/**
 * 简化版前端测试脚本
 * 专注于发现核心问题
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SimpleTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.issues = [];
  }

  async init() {
    console.log('🚀 启动浏览器...');

    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();

    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[${type.toUpperCase()}] ${text}`);

      if (type === 'error') {
        this.issues.push({
          type: 'console_error',
          message: text,
          timestamp: new Date().toISOString()
        });
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
      this.issues.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
    });

    // 监听网络请求失败
    this.page.on('requestfailed', request => {
      console.warn('⚠️ 网络请求失败:', request.url(), request.failure().errorText);
      this.issues.push({
        type: 'network_error',
        url: request.url(),
        failure: request.failure().errorText,
        timestamp: new Date().toISOString()
      });
    });
  }

  async takeScreenshot(name) {
    const filename = `screenshot-${name}-${Date.now()}.png`;
    const filepath = path.join(__dirname, 'test-screenshots', filename);

    // 确保目录存在
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    await this.page.screenshot({ path: filepath, fullPage: true });
    console.log(`📸 截图保存: ${filename}`);
    return filepath;
  }

  async testHomePage() {
    console.log('\n🏠 测试首页...');

    try {
      // 导航到首页
      console.log('导航到 http://localhost:5173');
      const response = await this.page.goto('http://localhost:5173', {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      console.log(`HTTP状态: ${response.status()}`);

      // 等待页面渲染
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 截图
      await this.takeScreenshot('homepage');

      // 检查页面标题
      const title = await this.page.title();
      console.log(`页面标题: ${title}`);

      // 检查Vue应用
      const hasVueApp = await this.page.evaluate(() => {
        return document.querySelector('#app') !== null;
      });
      console.log(`Vue应用存在: ${hasVueApp}`);

      // 检查页面内容
      const appContent = await this.page.evaluate(() => {
        const app = document.querySelector('#app');
        return app ? app.innerHTML.substring(0, 500) : 'No content';
      });
      console.log(`应用内容预览: ${appContent.substring(0, 200)}...`);

      // 检查是否有导航栏
      const hasNavbar = await this.page.evaluate(() => {
        return document.querySelector('.navbar-nav, .top-navbar, nav') !== null;
      });
      console.log(`导航栏存在: ${hasNavbar}`);

      // 检查所有可见的链接
      const links = await this.page.evaluate(() => {
        const allLinks = Array.from(document.querySelectorAll('a[href]'));
        return allLinks.map(link => ({
          text: link.textContent.trim(),
          href: link.getAttribute('href'),
          visible: link.offsetParent !== null
        })).filter(link => link.visible && link.text);
      });

      console.log(`找到 ${links.length} 个可见链接:`);
      links.forEach(link => {
        console.log(`  - ${link.text} -> ${link.href}`);
      });

      // 检查按钮
      const buttons = await this.page.evaluate(() => {
        const allButtons = Array.from(document.querySelectorAll('button'));
        return allButtons.map(btn => ({
          text: btn.textContent.trim(),
          visible: btn.offsetParent !== null,
          disabled: btn.disabled
        })).filter(btn => btn.visible && btn.text);
      });

      console.log(`找到 ${buttons.length} 个可见按钮:`);
      buttons.forEach(btn => {
        console.log(`  - ${btn.text} ${btn.disabled ? '(禁用)' : ''}`);
      });

      return { success: true, links, buttons };

    } catch (error) {
      console.error('❌ 首页测试失败:', error.message);
      await this.takeScreenshot('homepage-error');
      return { success: false, error: error.message };
    }
  }

  async testNavigation(links) {
    console.log('\n🧭 测试导航...');

    for (const link of links.slice(0, 5)) { // 只测试前5个链接
      if (!link.href || link.href === '#' || link.href.startsWith('javascript:')) {
        continue;
      }

      try {
        console.log(`\n测试链接: ${link.text} -> ${link.href}`);

        let fullUrl;
        if (link.href.startsWith('/')) {
          fullUrl = `http://localhost:5173${link.href}`;
        } else if (link.href.startsWith('http')) {
          fullUrl = link.href;
        } else {
          fullUrl = `http://localhost:5173/${link.href}`;
        }

        const response = await this.page.goto(fullUrl, {
          waitUntil: 'networkidle0',
          timeout: 15000
        });

        console.log(`  状态: ${response.status()}`);

        // 等待页面渲染
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 截图
        const pageName = link.text.toLowerCase().replace(/[^a-z0-9]/g, '-');
        await this.takeScreenshot(`page-${pageName}`);

        // 检查页面内容
        const pageContent = await this.page.evaluate(() => {
          const body = document.body;
          return {
            hasContent: body.children.length > 0,
            textLength: body.textContent.trim().length,
            title: document.title
          };
        });

        console.log(`  内容长度: ${pageContent.textLength} 字符`);
        console.log(`  页面标题: ${pageContent.title}`);

        // 检查是否有错误信息
        const hasError = await this.page.evaluate(() => {
          const text = document.body.textContent.toLowerCase();
          return text.includes('error') || text.includes('错误') ||
                 text.includes('404') || text.includes('not found');
        });

        if (hasError) {
          console.log('  ⚠️ 页面可能包含错误信息');
        }

      } catch (error) {
        console.error(`  ❌ 导航失败: ${error.message}`);
        await this.takeScreenshot(`nav-error-${link.text.replace(/[^a-z0-9]/g, '-')}`);
      }
    }
  }

  async testInteractivity() {
    console.log('\n🖱️ 测试交互功能...');

    try {
      // 回到首页
      await this.page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 查找并测试按钮
      const buttons = await this.page.$$('button:not([disabled])');
      console.log(`找到 ${buttons.length} 个可点击按钮`);

      for (let i = 0; i < Math.min(buttons.length, 3); i++) {
        try {
          const buttonText = await buttons[i].evaluate(el => el.textContent.trim());
          console.log(`点击按钮: ${buttonText}`);

          await buttons[i].click();
          await this.page.waitForTimeout(1000);

          // 检查是否有变化
          const afterClick = await this.page.evaluate(() => document.body.textContent.length);
          console.log(`  点击后页面内容长度: ${afterClick}`);

        } catch (error) {
          console.log(`  按钮点击失败: ${error.message}`);
        }
      }

      // 查找并测试输入框
      const inputs = await this.page.$$('input[type="text"], input:not([type])');
      console.log(`找到 ${inputs.length} 个输入框`);

      for (let i = 0; i < Math.min(inputs.length, 2); i++) {
        try {
          const placeholder = await inputs[i].evaluate(el => el.placeholder || '无占位符');
          console.log(`测试输入框: ${placeholder}`);

          await inputs[i].click();
          await inputs[i].type('测试输入');
          await this.page.waitForTimeout(500);

          const value = await inputs[i].evaluate(el => el.value);
          console.log(`  输入值: ${value}`);

        } catch (error) {
          console.log(`  输入框测试失败: ${error.message}`);
        }
      }

    } catch (error) {
      console.error('❌ 交互测试失败:', error.message);
    }
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      issues: this.issues,
      summary: {
        totalIssues: this.issues.length,
        consoleErrors: this.issues.filter(i => i.type === 'console_error').length,
        pageErrors: this.issues.filter(i => i.type === 'page_error').length,
        networkErrors: this.issues.filter(i => i.type === 'network_error').length
      }
    };

    const reportPath = path.join(__dirname, `simple-test-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log('\n📊 测试总结:');
    console.log(`总问题数: ${report.summary.totalIssues}`);
    console.log(`控制台错误: ${report.summary.consoleErrors}`);
    console.log(`页面错误: ${report.summary.pageErrors}`);
    console.log(`网络错误: ${report.summary.networkErrors}`);
    console.log(`详细报告: ${reportPath}`);

    return report;
  }

  async runTest() {
    try {
      await this.init();

      console.log('🎯 开始简化测试...');

      // 1. 测试首页
      const homeResult = await this.testHomePage();

      if (homeResult.success) {
        // 2. 测试导航
        await this.testNavigation(homeResult.links);

        // 3. 测试交互
        await this.testInteractivity();
      }

      // 4. 生成报告
      await this.generateReport();

      console.log('\n🎉 测试完成！');

    } catch (error) {
      console.error('❌ 测试失败:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行测试
const tester = new SimpleTester();
tester.runTest().catch(console.error);
